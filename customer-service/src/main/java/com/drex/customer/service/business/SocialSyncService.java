package com.drex.customer.service.business;

/**
 * 社媒信息同步服务
 * <AUTHOR>
 */
public interface SocialSyncService {
    
    /**
     * 同步所有passport的社媒信息到customerBind表
     * @return 同步结果统计信息
     */
    SyncResult syncAllPassportSocialInfo();
    
    /**
     * 同步结果统计
     */
    class SyncResult {
        private int totalProcessed;
        private int successCount;
        private int skipCount;
        private int errorCount;
        private String message;
        
        public SyncResult() {}
        
        public SyncResult(int totalProcessed, int successCount, int skipCount, int errorCount, String message) {
            this.totalProcessed = totalProcessed;
            this.successCount = successCount;
            this.skipCount = skipCount;
            this.errorCount = errorCount;
            this.message = message;
        }
        
        // Getters and Setters
        public int getTotalProcessed() { return totalProcessed; }
        public void setTotalProcessed(int totalProcessed) { this.totalProcessed = totalProcessed; }
        
        public int getSuccessCount() { return successCount; }
        public void setSuccessCount(int successCount) { this.successCount = successCount; }
        
        public int getSkipCount() { return skipCount; }
        public void setSkipCount(int skipCount) { this.skipCount = skipCount; }
        
        public int getErrorCount() { return errorCount; }
        public void setErrorCount(int errorCount) { this.errorCount = errorCount; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        @Override
        public String toString() {
            return String.format("SyncResult{totalProcessed=%d, successCount=%d, skipCount=%d, errorCount=%d, message='%s'}", 
                    totalProcessed, successCount, skipCount, errorCount, message);
        }
    }
}
