package com.drex.customer.service.business.impl;

import com.drex.customer.api.constants.WalletConstant;
import com.drex.customer.api.response.PassportConnectDTO;
import com.drex.customer.dal.tablestore.builder.PassportBuilder;
import com.drex.customer.dal.tablestore.model.CustomerBind;
import com.drex.customer.dal.tablestore.model.Passport;
import com.drex.customer.service.business.CustomerBindService;
import com.drex.customer.service.business.PassportService;
import com.drex.customer.service.business.SocialSyncService;
import com.drex.customer.service.business.ThirdWebService;
import com.drex.customer.service.config.CustomerProperties;
import com.kikitrade.framework.common.model.TokenPage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 社媒信息同步服务实现
 * <AUTHOR>
 */
@Service
@Slf4j
public class SocialSyncServiceImpl implements SocialSyncService {
    
    @Resource
    private PassportBuilder passportBuilder;
    
    @Resource
    private PassportService passportService;
    
    @Resource
    private ThirdWebService thirdWebService;
    
    @Resource
    private CustomerBindService customerBindService;
    
    @Resource
    private CustomerProperties customerProperties;

    @Resource
    @Qualifier("socialSyncExecutor")
    private ScheduledExecutorService scheduledExecutorService;


    
    @Override
    public SyncResult syncAllPassportSocialInfo() {
        log.info("开始同步所有passport的社媒信息到customerBind表");
        
        AtomicInteger totalProcessed = new AtomicInteger(0);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger skipCount = new AtomicInteger(0);
        AtomicInteger errorCount = new AtomicInteger(0);
        
        String nextToken = null;
        Integer batchSize = customerProperties.getSyncSocialBatchSize();
        Integer delaySeconds = customerProperties.getSyncSocialDelaySeconds();
        
        try {
            do {
                // 分页查询passport记录
                TokenPage<Passport> passportPage = passportBuilder.listAllPassport(batchSize, nextToken);
                List<Passport> passports = passportPage.getRows();
                
                if (!CollectionUtils.isEmpty(passports)) {
                    log.info("处理第{}批passport记录，数量: {}", totalProcessed.get() / batchSize + 1, passports.size());
                    
                    // 处理当前批次的passport记录
                    for (Passport passport : passports) {
                        scheduledExecutorService.schedule(() -> {
                            processPassportSocialInfo(passport, successCount, skipCount, errorCount);
                        }, delaySeconds, TimeUnit.SECONDS);
                        
                        totalProcessed.incrementAndGet();
                    }
                }
                
                nextToken = passportPage.getNextToken();
                
                // 批次间延迟
                if (StringUtils.isNotBlank(nextToken)) {
                    try {
                        Thread.sleep(delaySeconds * 1000L);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.warn("批次间延迟被中断");
                        break;
                    }
                }
                
            } while (StringUtils.isNotBlank(nextToken));
            
            String message = String.format("同步任务已提交，总计处理%d条记录", totalProcessed.get());
            log.info(message);
            
            return new SyncResult(totalProcessed.get(), successCount.get(), skipCount.get(), 
                    errorCount.get(), message);
            
        } catch (Exception e) {
            log.error("同步passport社媒信息时发生错误", e);
            return new SyncResult(totalProcessed.get(), successCount.get(), skipCount.get(), 
                    errorCount.get(), "同步过程中发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 处理单个passport的社媒信息
     */
    private void processPassportSocialInfo(Passport passport, AtomicInteger successCount, 
                                         AtomicInteger skipCount, AtomicInteger errorCount) {
        try {
            String passportId = passport.getPassportId();
            log.info("开始处理passport: {}", passportId);
            
            // 获取passport关联的钱包连接信息
            List<PassportConnectDTO> passportConnects = passportService.getPassportConnect(passportId);
            
            if (CollectionUtils.isEmpty(passportConnects)) {
                log.info("passport {} 没有关联的钱包连接信息，跳过", passportId);
                skipCount.incrementAndGet();
                return;
            }
            
            boolean hasProcessed = false;
            
            // 遍历每个钱包连接，查询社媒信息
            for (PassportConnectDTO connect : passportConnects) {
                if (StringUtils.isBlank(connect.getWalletAddress()) || 
                    StringUtils.isBlank(connect.getSubConnectProvider())) {
                    continue;
                }
                
                // 跳过钱包类型的连接
                if ("wallet".equalsIgnoreCase(connect.getSubConnectProvider())) {
                    continue;
                }
                
                WalletConstant.PlatformEnum platformEnum = WalletConstant.PlatformEnum.getEnumByName(
                        connect.getSubConnectProvider());
                if (platformEnum == null) {
                    log.info("不支持的平台类型: {}", connect.getSubConnectProvider());
                    continue;
                }
                
                // 检查是否已经存在绑定记录
                CustomerBind existingBind = customerBindService.findByCustomerId(passportId, platformEnum.name());
                if (existingBind != null) {
                    if (WalletConstant.PlatformEnum.X == platformEnum) {
                        if (StringUtils.isNotBlank(existingBind.getSocialUserId())) {
                            log.debug("passport {} 平台 X 已存在有效绑定记录，跳过", passportId);
                            continue;
                        }
                        log.info("passport {} 平台 X 存在无效绑定记录，将尝试更新", passportId);
                    } else {
                        log.info("passport {} 平台 {} 已存在绑定记录，跳过", passportId, platformEnum.name());
                        continue;
                    }
                }
                
                // 调用ThirdWebService获取社媒信息
                ThirdWebService.ThirdWebUserAccount userAccount = thirdWebService.getThirdWebUserAccount(
                        connect.getWalletAddress(), platformEnum);
                
                if (userAccount != null) {
                    // 创建CustomerBind记录
                    CustomerBind customerBind = createCustomerBind(passportId, platformEnum, userAccount);
                    
                    // 插入到数据库
                    boolean inserted = customerBindService.insert(customerBind);
                    if (inserted) {
                        log.info("成功为passport {} 绑定社媒平台 {}", passportId, platformEnum.name());
                        hasProcessed = true;
                    } else {
                        log.warn("为passport {} 绑定社媒平台 {} 失败", passportId, platformEnum.name());
                    }
                } else {
                    log.info("passport {} 在ThirdWeb中未找到 {} 平台的社媒信息", passportId, platformEnum.name());
                }
            }
            
            if (hasProcessed) {
                successCount.incrementAndGet();
            } else {
                skipCount.incrementAndGet();
            }
            
        } catch (Exception e) {
            log.error("处理passport {} 的社媒信息时发生错误", passport.getPassportId(), e);
            errorCount.incrementAndGet();
        }
    }
    
    /**
     * 创建CustomerBind对象
     */
    private CustomerBind createCustomerBind(String passportId, WalletConstant.PlatformEnum platform, 
                                          ThirdWebService.ThirdWebUserAccount userAccount) {
        CustomerBind customerBind = new CustomerBind();
        customerBind.setCustomerId(passportId);
        customerBind.setSocialPlatform(platform.name());
        customerBind.setSocialUserId(userAccount.getId());
        customerBind.setSocialHandleName(userAccount.getUsername());
        customerBind.setSocialEmail(userAccount.getEmail());
        customerBind.setPrivacyAuth(false); // 默认不授权隐私
        customerBind.setCreated(System.currentTimeMillis());
        
        return customerBind;
    }
}
